<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教室管理 - 特靠谱教培系统</title>
    <!-- Bootstrap CSS -->
    <link href="../../web/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../web/css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        .card-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .classroom-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            background: white;
            transition: all 0.3s;
        }
        .classroom-card:hover {
            border-color: #6f42c1;
            box-shadow: 0 4px 12px rgba(111, 66, 193, 0.15);
        }
        .classroom-status {
            font-size: 0.8rem;
            padding: 4px 8px;
            border-radius: 12px;
        }
        .status-available {
            background-color: #d1edff;
            color: #0c5460;
        }
        .status-occupied {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-maintenance {
            background-color: #f8d7da;
            color: #721c24;
        }
        .filter-tabs {
            margin-bottom: 20px;
        }
        .filter-tab {
            border: none;
            background: none;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 25px;
            color: #6c757d;
            transition: all 0.3s;
        }
        .filter-tab.active {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
        }
        .search-box {
            margin-bottom: 20px;
        }
        .btn-add-classroom {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            border: none;
            color: white;
            border-radius: 25px;
            padding: 10px 25px;
            transition: transform 0.2s;
        }
        .btn-add-classroom:hover {
            transform: translateY(-2px);
            color: white;
        }
        .classroom-info {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .classroom-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-right: 15px;
        }
        .classroom-details {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
        }
        .classroom-schedule {
            max-height: 200px;
            overflow-y: auto;
        }
        .schedule-item {
            border-left: 4px solid #6f42c1;
            padding: 10px;
            margin-bottom: 8px;
            background: white;
            border-radius: 0 5px 5px 0;
            font-size: 0.9rem;
        }
        .classroom-actions {
            border-top: 1px solid #e9ecef;
            padding-top: 15px;
            margin-top: 15px;
        }
        .btn-action {
            padding: 5px 15px;
            font-size: 0.8rem;
            border-radius: 15px;
            margin: 0 5px;
        }
        .stats-card {
            text-align: center;
            padding: 20px;
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #6f42c1;
        }
        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .equipment-list {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }
        .equipment-tag {
            background-color: #e9ecef;
            border-radius: 12px;
            padding: 3px 8px;
            font-size: 0.7rem;
            color: #495057;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fa fa-home"></i> 主页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.html">
                            <i class="fa fa-users"></i> 用户管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.html">
                            <i class="fa fa-book"></i> 课程管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="classrooms.html">
                            <i class="fa fa-building"></i> 教室管理
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-user-shield"></i> 管理员
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fa fa-user"></i> 个人信息</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fa fa-cog"></i> 系统设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../index.html"><i class="fa fa-sign-out"></i> 退出</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-md-8">
                <h2><i class="fa fa-building"></i> 教室管理</h2>
                <p class="text-muted">管理教室资源，检测冲突和安排维护</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-add-classroom" data-bs-toggle="modal" data-bs-target="#addClassroomModal">
                    <i class="fa fa-plus"></i> 添加教室
                </button>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card">
                    <div class="card-body stats-card">
                        <div class="stats-number">8</div>
                        <div class="stats-label">教室总数</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card">
                    <div class="card-body stats-card">
                        <div class="stats-number">5</div>
                        <div class="stats-label">使用中</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card">
                    <div class="card-body stats-card">
                        <div class="stats-number">2</div>
                        <div class="stats-label">空闲</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card">
                    <div class="card-body stats-card">
                        <div class="stats-number">1</div>
                        <div class="stats-label">维护中</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="search-box">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="搜索教室编号或位置..." id="searchInput">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fa fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="filter-tabs text-end">
                    <button class="filter-tab active" onclick="filterClassrooms('all')">全部</button>
                    <button class="filter-tab" onclick="filterClassrooms('available')">空闲</button>
                    <button class="filter-tab" onclick="filterClassrooms('occupied')">使用中</button>
                    <button class="filter-tab" onclick="filterClassrooms('maintenance')">维护中</button>
                </div>
            </div>
        </div>

        <!-- 教室列表 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-list"></i> 教室列表</h5>
                    </div>
                    <div class="card-body">
                        <!-- 教室A101 -->
                        <div class="classroom-card" data-status="occupied">
                            <div class="classroom-info">
                                <div class="classroom-icon">
                                    <i class="fa fa-chalkboard"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">教室A101</h6>
                                    <small class="text-muted">一楼东侧 | 容量：30人</small>
                                </div>
                                <div class="ms-auto">
                                    <span class="classroom-status status-occupied">使用中</span>
                                </div>
                            </div>
                            
                            <div class="classroom-details">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>楼层：</strong>1楼
                                    </div>
                                    <div class="col-md-3">
                                        <strong>面积：</strong>60㎡
                                    </div>
                                    <div class="col-md-3">
                                        <strong>座位：</strong>30个
                                    </div>
                                    <div class="col-md-3">
                                        <strong>类型：</strong>普通教室
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <strong>设备：</strong>
                                        <div class="equipment-list">
                                            <span class="equipment-tag">投影仪</span>
                                            <span class="equipment-tag">音响</span>
                                            <span class="equipment-tag">空调</span>
                                            <span class="equipment-tag">白板</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="classroom-schedule">
                                <strong>今日课程安排：</strong>
                                <div class="schedule-item">
                                    <div>09:00-10:30 | 数学-代数基础 | 李老师</div>
                                </div>
                                <div class="schedule-item">
                                    <div>14:00-15:30 | 数学-几何基础 | 李老师</div>
                                </div>
                                <div class="schedule-item">
                                    <div>16:00-17:30 | 数学-综合练习 | 李老师</div>
                                </div>
                            </div>
                            
                            <div class="classroom-actions">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <small class="text-muted">最后更新：2025-01-21 08:30 | 使用率：85%</small>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <button class="btn btn-outline-primary btn-action" onclick="editClassroom('A101')">
                                            <i class="fa fa-edit"></i> 编辑
                                        </button>
                                        <button class="btn btn-outline-info btn-action" onclick="viewSchedule('A101')">
                                            <i class="fa fa-calendar"></i> 课表
                                        </button>
                                        <button class="btn btn-outline-warning btn-action" onclick="maintenanceClassroom('A101')">
                                            <i class="fa fa-wrench"></i> 维护
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 教室B203 -->
                        <div class="classroom-card" data-status="available">
                            <div class="classroom-info">
                                <div class="classroom-icon">
                                    <i class="fa fa-chalkboard"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">教室B203</h6>
                                    <small class="text-muted">二楼西侧 | 容量：25人</small>
                                </div>
                                <div class="ms-auto">
                                    <span class="classroom-status status-available">空闲</span>
                                </div>
                            </div>
                            
                            <div class="classroom-details">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>楼层：</strong>2楼
                                    </div>
                                    <div class="col-md-3">
                                        <strong>面积：</strong>50㎡
                                    </div>
                                    <div class="col-md-3">
                                        <strong>座位：</strong>25个
                                    </div>
                                    <div class="col-md-3">
                                        <strong>类型：</strong>多媒体教室
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <strong>设备：</strong>
                                        <div class="equipment-list">
                                            <span class="equipment-tag">智能投影</span>
                                            <span class="equipment-tag">音响系统</span>
                                            <span class="equipment-tag">空调</span>
                                            <span class="equipment-tag">电子白板</span>
                                            <span class="equipment-tag">网络</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="classroom-schedule">
                                <strong>今日课程安排：</strong>
                                <div class="text-muted">暂无课程安排</div>
                            </div>
                            
                            <div class="classroom-actions">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <small class="text-muted">最后更新：2025-01-20 16:00 | 使用率：45%</small>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <button class="btn btn-outline-primary btn-action" onclick="editClassroom('B203')">
                                            <i class="fa fa-edit"></i> 编辑
                                        </button>
                                        <button class="btn btn-outline-success btn-action" onclick="bookClassroom('B203')">
                                            <i class="fa fa-plus"></i> 预约
                                        </button>
                                        <button class="btn btn-outline-warning btn-action" onclick="maintenanceClassroom('B203')">
                                            <i class="fa fa-wrench"></i> 维护
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 实验室C301 -->
                        <div class="classroom-card" data-status="maintenance">
                            <div class="classroom-info">
                                <div class="classroom-icon">
                                    <i class="fa fa-flask"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">实验室C301</h6>
                                    <small class="text-muted">三楼北侧 | 容量：20人</small>
                                </div>
                                <div class="ms-auto">
                                    <span class="classroom-status status-maintenance">维护中</span>
                                </div>
                            </div>
                            
                            <div class="classroom-details">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>楼层：</strong>3楼
                                    </div>
                                    <div class="col-md-3">
                                        <strong>面积：</strong>80㎡
                                    </div>
                                    <div class="col-md-3">
                                        <strong>座位：</strong>20个
                                    </div>
                                    <div class="col-md-3">
                                        <strong>类型：</strong>物理实验室
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <strong>设备：</strong>
                                        <div class="equipment-list">
                                            <span class="equipment-tag">实验台</span>
                                            <span class="equipment-tag">通风系统</span>
                                            <span class="equipment-tag">安全设备</span>
                                            <span class="equipment-tag">实验器材</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="classroom-schedule">
                                <strong>维护信息：</strong>
                                <div class="alert alert-warning mb-0 mt-2">
                                    <i class="fa fa-exclamation-triangle"></i> 
                                    正在进行设备维护，预计2025-01-25完成
                                </div>
                            </div>
                            
                            <div class="classroom-actions">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <small class="text-muted">维护开始：2025-01-22 | 预计完成：2025-01-25</small>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <button class="btn btn-outline-primary btn-action" onclick="editClassroom('C301')">
                                            <i class="fa fa-edit"></i> 编辑
                                        </button>
                                        <button class="btn btn-outline-success btn-action" onclick="completeMaintenance('C301')">
                                            <i class="fa fa-check"></i> 完成维护
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加教室模态框 -->
    <div class="modal fade" id="addClassroomModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加教室</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addClassroomForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">教室编号</label>
                                <input type="text" class="form-control" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">教室类型</label>
                                <select class="form-select" required>
                                    <option value="">请选择类型</option>
                                    <option value="普通教室">普通教室</option>
                                    <option value="多媒体教室">多媒体教室</option>
                                    <option value="实验室">实验室</option>
                                    <option value="会议室">会议室</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">楼层</label>
                                <input type="text" class="form-control" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">位置</label>
                                <input type="text" class="form-control" placeholder="如：东侧、西侧、北侧" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">面积（㎡）</label>
                                <input type="number" class="form-control" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">座位数</label>
                                <input type="number" class="form-control" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">设备配置</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="projector">
                                        <label class="form-check-label" for="projector">投影仪</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="audio">
                                        <label class="form-check-label" for="audio">音响系统</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="ac">
                                        <label class="form-check-label" for="ac">空调</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="whiteboard">
                                        <label class="form-check-label" for="whiteboard">白板</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="network">
                                        <label class="form-check-label" for="network">网络</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="other">
                                        <label class="form-check-label" for="other">其他设备</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">备注</label>
                            <textarea class="form-control" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="addClassroom()">添加教室</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../web/js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../web/bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 筛选教室
        function filterClassrooms(status) {
            // 更新筛选按钮状态
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 筛选教室列表
            const classrooms = document.querySelectorAll('.classroom-card');
            classrooms.forEach(classroom => {
                if (status === 'all' || classroom.dataset.status === status) {
                    classroom.style.display = 'block';
                } else {
                    classroom.style.display = 'none';
                }
            });
        }

        // 编辑教室
        function editClassroom(name) {
            alert(`编辑教室：${name}`);
        }

        // 查看课表
        function viewSchedule(name) {
            alert(`查看教室 ${name} 的课表安排`);
        }

        // 预约教室
        function bookClassroom(name) {
            alert(`预约教室：${name}`);
        }

        // 维护教室
        function maintenanceClassroom(name) {
            if (confirm(`确认将教室 ${name} 设置为维护状态？`)) {
                alert(`教室 ${name} 已设置为维护状态`);
            }
        }

        // 完成维护
        function completeMaintenance(name) {
            if (confirm(`确认完成教室 ${name} 的维护？`)) {
                alert(`教室 ${name} 维护已完成，恢复正常使用`);
            }
        }

        // 添加教室
        function addClassroom() {
            alert('教室添加成功');
            bootstrap.Modal.getInstance(document.getElementById('addClassroomModal')).hide();
        }

        // 搜索功能
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const classrooms = document.querySelectorAll('.classroom-card');
            
            classrooms.forEach(classroom => {
                const text = classroom.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    classroom.style.display = 'block';
                } else {
                    classroom.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
