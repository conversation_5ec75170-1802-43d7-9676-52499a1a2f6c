<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>家长绑定管理 - 特靠谱教培系统</title>
    <!-- Bootstrap CSS -->
    <link href="../../web/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../web/css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .student-card {
            border-left: 4px solid #6f42c1;
            margin-bottom: 15px;
        }
        .parent-item {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            margin: 5px 0;
            border-left: 3px solid #e83e8c;
        }
        .relation-badge {
            font-size: 0.8rem;
            padding: 4px 8px;
            border-radius: 12px;
        }
        .relation-father {
            background-color: #cce5ff;
            color: #004085;
        }
        .relation-mother {
            background-color: #ffe6f0;
            color: #6b1e3e;
        }
        .relation-grandparent {
            background-color: #fff3cd;
            color: #856404;
        }
        .relation-other {
            background-color: #e2e3e5;
            color: #383d41;
        }
        .btn-bind {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            border: none;
            border-radius: 25px;
            padding: 8px 20px;
            color: white;
            transition: all 0.3s;
        }
        .btn-bind:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(111, 66, 193, 0.3);
            color: white;
        }
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 10px 20px;
        }
        .search-box:focus {
            border-color: #6f42c1;
            box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
        }
        .stats-card {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
        }
        .teacher-filter {
            background-color: #fff;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);">
        <div class="container">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fa fa-graduation-cap me-2"></i>特靠谱教培系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fa fa-home me-1"></i>主页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.html">
                            <i class="fa fa-users me-1"></i>用户管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.html">
                            <i class="fa fa-book me-1"></i>课程管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classrooms.html">
                            <i class="fa fa-building me-1"></i>教室管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="parent-binding.html">
                            <i class="fa fa-link me-1"></i>家长绑定
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-user me-1"></i>管理员
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fa fa-user me-2"></i>个人资料</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fa fa-cog me-2"></i>系统设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../index.html"><i class="fa fa-sign-out me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fa fa-link text-primary me-2"></i>家长绑定管理</h2>
                <p class="text-muted">全局管理学生与家长微信账号的绑定关系</p>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number">156</div>
                    <div>总学生数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number">298</div>
                    <div>已绑定家长</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number">23</div>
                    <div>未绑定学生</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number">91%</div>
                    <div>绑定率</div>
                </div>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="teacher-filter">
            <div class="row">
                <div class="col-md-3">
                    <label class="form-label">选择教师</label>
                    <select class="form-select" id="teacherFilter">
                        <option value="">全部教师</option>
                        <option value="teacher1">张老师</option>
                        <option value="teacher2">李老师</option>
                        <option value="teacher3">王老师</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">选择班级</label>
                    <select class="form-select" id="classFilter">
                        <option value="">全部班级</option>
                        <option value="class1">高一(1)班</option>
                        <option value="class2">高一(2)班</option>
                        <option value="class3">高二(1)班</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">绑定状态</label>
                    <select class="form-select" id="statusFilter">
                        <option value="">全部状态</option>
                        <option value="bound">已绑定</option>
                        <option value="unbound">未绑定</option>
                        <option value="partial">部分绑定</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">搜索学生</label>
                    <input type="text" class="form-control search-box" placeholder="学生姓名或学号..." id="searchStudent">
                </div>
            </div>
        </div>

        <!-- 批量操作 -->
        <div class="row mb-3">
            <div class="col-md-6">
                <button class="btn btn-bind" data-bs-toggle="modal" data-bs-target="#batchBindingModal">
                    <i class="fa fa-plus me-2"></i>批量绑定
                </button>
                <button class="btn btn-outline-danger ms-2" onclick="batchUnbind()">
                    <i class="fa fa-unlink me-2"></i>批量解绑
                </button>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-outline-success" onclick="exportData()">
                    <i class="fa fa-download me-2"></i>导出数据
                </button>
            </div>
        </div>

        <!-- 学生列表 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fa fa-list me-2"></i>学生家长绑定列表</h5>
                            <div>
                                <input type="checkbox" class="form-check-input me-2" id="selectAll">
                                <label for="selectAll">全选</label>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- 学生1 -->
                        <div class="student-card card" data-teacher="张老师" data-class="高一(1)班" data-status="bound">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-1">
                                        <input type="checkbox" class="form-check-input student-checkbox">
                                    </div>
                                    <div class="col-md-3">
                                        <h6 class="mb-1">李小明</h6>
                                        <small class="text-muted">学号：2024001 | 高一(1)班 | 张老师</small>
                                    </div>
                                    <div class="col-md-5">
                                        <div class="parent-item">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong>李爸爸</strong>
                                                    <span class="relation-badge relation-father ms-2">父亲</span>
                                                </div>
                                                <div>
                                                    <small class="text-muted">微信：已绑定 | 最后登录：2天前</small>
                                                    <button class="btn btn-sm btn-outline-danger ms-2">解绑</button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="parent-item">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong>李妈妈</strong>
                                                    <span class="relation-badge relation-mother ms-2">母亲</span>
                                                </div>
                                                <div>
                                                    <small class="text-muted">微信：已绑定 | 最后登录：1天前</small>
                                                    <button class="btn btn-sm btn-outline-danger ms-2">解绑</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 text-end">
                                        <button class="btn btn-bind btn-sm" onclick="addParent('李小明')">
                                            <i class="fa fa-plus me-1"></i>添加家长
                                        </button>
                                        <button class="btn btn-outline-info btn-sm ms-1" onclick="viewHistory('李小明')">
                                            <i class="fa fa-history me-1"></i>历史
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 学生2 -->
                        <div class="student-card card" data-teacher="张老师" data-class="高一(1)班" data-status="partial">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-1">
                                        <input type="checkbox" class="form-check-input student-checkbox">
                                    </div>
                                    <div class="col-md-3">
                                        <h6 class="mb-1">王小红</h6>
                                        <small class="text-muted">学号：2024002 | 高一(1)班 | 张老师</small>
                                    </div>
                                    <div class="col-md-5">
                                        <div class="parent-item">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong>王奶奶</strong>
                                                    <span class="relation-badge relation-grandparent ms-2">奶奶</span>
                                                </div>
                                                <div>
                                                    <small class="text-muted">微信：已绑定 | 最后登录：5天前</small>
                                                    <button class="btn btn-sm btn-outline-danger ms-2">解绑</button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="text-muted">
                                            <small><i class="fa fa-exclamation-triangle text-warning me-1"></i>建议添加父母联系方式</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3 text-end">
                                        <button class="btn btn-bind btn-sm" onclick="addParent('王小红')">
                                            <i class="fa fa-plus me-1"></i>添加家长
                                        </button>
                                        <button class="btn btn-outline-info btn-sm ms-1" onclick="viewHistory('王小红')">
                                            <i class="fa fa-history me-1"></i>历史
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 学生3 -->
                        <div class="student-card card" data-teacher="李老师" data-class="高一(2)班" data-status="unbound">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-1">
                                        <input type="checkbox" class="form-check-input student-checkbox">
                                    </div>
                                    <div class="col-md-3">
                                        <h6 class="mb-1">张小华</h6>
                                        <small class="text-muted">学号：2024003 | 高一(2)班 | 李老师</small>
                                    </div>
                                    <div class="col-md-5">
                                        <div class="alert alert-warning py-2 mb-0">
                                            <small><i class="fa fa-exclamation-triangle me-1"></i>暂无家长绑定，建议尽快联系家长</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3 text-end">
                                        <button class="btn btn-bind btn-sm" onclick="addParent('张小华')">
                                            <i class="fa fa-plus me-1"></i>添加家长
                                        </button>
                                        <button class="btn btn-outline-info btn-sm ms-1" onclick="viewHistory('张小华')">
                                            <i class="fa fa-history me-1"></i>历史
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 分页 -->
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <div>
                                <small class="text-muted">显示 1-10 条，共 156 条记录</small>
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm mb-0">
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#">上一页</a>
                                    </li>
                                    <li class="page-item active">
                                        <a class="page-link" href="#">1</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="#">2</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="#">3</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="#">下一页</a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量绑定模态框 -->
    <div class="modal fade" id="batchBindingModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); color: white;">
                    <h5 class="modal-title">批量家长绑定</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle me-2"></i>
                        您可以为多个学生同时生成绑定码，方便批量分发给家长
                    </div>
                    <form id="batchBindingForm">
                        <div class="mb-3">
                            <label class="form-label">选择学生（已选择 <span id="selectedCount">0</span> 个）</label>
                            <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="2024001" id="batch_2024001">
                                    <label class="form-check-label" for="batch_2024001">
                                        李小明 (2024001) - 高一(1)班
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="2024002" id="batch_2024002">
                                    <label class="form-check-label" for="batch_2024002">
                                        王小红 (2024002) - 高一(1)班
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="2024003" id="batch_2024003">
                                    <label class="form-check-label" for="batch_2024003">
                                        张小华 (2024003) - 高一(2)班
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">绑定方式</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="batchBindMethod" id="batchQrCode" value="qr" checked>
                                <label class="form-check-label" for="batchQrCode">
                                    为每个学生生成独立二维码
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="batchBindMethod" id="batchInviteCode" value="code">
                                <label class="form-check-label" for="batchInviteCode">
                                    为每个学生生成独立邀请码
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-bind" onclick="submitBatchBinding()">生成批量绑定码</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 历史记录模态框 -->
    <div class="modal fade" id="historyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); color: white;">
                    <h5 class="modal-title">绑定历史记录</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6>李妈妈绑定成功</h6>
                                <p class="text-muted mb-1">关系：母亲 | 微信昵称：李妈妈</p>
                                <small class="text-muted">2025-08-20 14:30:25 | 操作人：张老师</small>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6>李爸爸绑定成功</h6>
                                <p class="text-muted mb-1">关系：父亲 | 微信昵称：李爸爸</p>
                                <small class="text-muted">2025-08-19 09:15:42 | 操作人：张老师</small>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6>生成绑定码</h6>
                                <p class="text-muted mb-1">为李小明生成家长绑定二维码</p>
                                <small class="text-muted">2025-08-19 09:10:15 | 操作人：张老师</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="../../web/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="../../web/js/jquery.min.js"></script>

    <script>
        // 添加家长绑定 - 修订时间：2025-08-21 23:45:48
        function addParent(studentName) {
            alert('为 ' + studentName + ' 添加家长绑定功能');
        }

        // 查看历史记录 - 修订时间：2025-08-21 23:45:48
        function viewHistory(studentName) {
            const modal = new bootstrap.Modal(document.getElementById('historyModal'));
            modal.show();
        }

        // 批量解绑 - 修订时间：2025-08-21 23:45:48
        function batchUnbind() {
            const selected = document.querySelectorAll('.student-checkbox:checked');
            if (selected.length === 0) {
                alert('请先选择要解绑的学生');
                return;
            }
            if (confirm('确定要解绑选中的 ' + selected.length + ' 个学生的家长绑定吗？')) {
                alert('批量解绑功能执行中...');
            }
        }

        // 导出数据 - 修订时间：2025-08-21 23:45:48
        function exportData() {
            alert('正在导出家长绑定数据...');
        }

        // 提交批量绑定 - 修订时间：2025-08-21 23:45:48
        function submitBatchBinding() {
            const selected = document.querySelectorAll('#batchBindingModal input[type="checkbox"]:checked');
            if (selected.length === 0) {
                alert('请先选择要绑定的学生');
                return;
            }
            alert('为 ' + selected.length + ' 个学生生成绑定码');
        }

        // 全选功能 - 修订时间：2025-08-21 23:45:48
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.student-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // 筛选功能 - 修订时间：2025-08-21 23:45:48
        function applyFilters() {
            const teacherFilter = document.getElementById('teacherFilter').value;
            const classFilter = document.getElementById('classFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            const searchTerm = document.getElementById('searchStudent').value.toLowerCase();

            const studentCards = document.querySelectorAll('.student-card');

            studentCards.forEach(card => {
                const teacher = card.dataset.teacher;
                const className = card.dataset.class;
                const status = card.dataset.status;
                const studentInfo = card.querySelector('h6').textContent.toLowerCase() +
                                  card.querySelector('small').textContent.toLowerCase();

                let show = true;

                if (teacherFilter && !teacher.includes(teacherFilter.replace('teacher', ''))) {
                    show = false;
                }

                if (classFilter && !className.includes(classFilter.replace('class', '高一(').replace('1', '1)班'))) {
                    show = false;
                }

                if (statusFilter && status !== statusFilter) {
                    show = false;
                }

                if (searchTerm && !studentInfo.includes(searchTerm)) {
                    show = false;
                }

                card.style.display = show ? 'block' : 'none';
            });
        }

        // 绑定筛选事件 - 修订时间：2025-08-21 23:45:48
        document.getElementById('teacherFilter').addEventListener('change', applyFilters);
        document.getElementById('classFilter').addEventListener('change', applyFilters);
        document.getElementById('statusFilter').addEventListener('change', applyFilters);
        document.getElementById('searchStudent').addEventListener('input', applyFilters);

        // 批量选择计数 - 修订时间：2025-08-21 23:45:48
        document.querySelectorAll('#batchBindingModal input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const selected = document.querySelectorAll('#batchBindingModal input[type="checkbox"]:checked');
                document.getElementById('selectedCount').textContent = selected.length;
            });
        });
    </script>
</body>
</html>
