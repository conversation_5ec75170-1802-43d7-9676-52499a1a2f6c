<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课表查看 - 特靠谱教培系统</title>
    <!-- Bootstrap CSS -->
    <link href="../../web/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../web/css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .schedule-table {
            font-size: 0.9rem;
        }
        .schedule-table th {
            background-color: #f8f9fa;
            border: none;
            text-align: center;
            padding: 15px 8px;
            font-weight: 600;
        }
        .schedule-table td {
            border: 1px solid #e9ecef;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
            height: 80px;
        }
        .time-slot {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        .course-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            padding: 8px;
            margin: 2px 0;
            font-size: 0.8rem;
            line-height: 1.2;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .course-item:hover {
            transform: scale(1.05);
        }
        .course-subject {
            font-weight: bold;
            margin-bottom: 2px;
        }
        .course-teacher {
            font-size: 0.7rem;
            opacity: 0.9;
        }
        .course-room {
            font-size: 0.7rem;
            opacity: 0.8;
        }
        .view-toggle {
            margin-bottom: 20px;
        }
        .btn-toggle {
            border-radius: 25px;
            padding: 8px 20px;
            margin: 0 5px;
        }
        .week-navigation {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }
        .week-nav-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #667eea;
            padding: 10px;
            cursor: pointer;
        }
        .week-nav-btn:hover {
            color: #764ba2;
        }
        .current-week {
            margin: 0 20px;
            font-weight: bold;
            color: #495057;
        }
        @media (max-width: 768px) {
            .schedule-table {
                font-size: 0.7rem;
            }
            .schedule-table td {
                height: 60px;
                padding: 4px;
            }
            .course-item {
                padding: 4px;
                font-size: 0.7rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fa fa-home"></i> 主页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="schedule.html">
                            <i class="fa fa-calendar"></i> 课表
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="leave.html">
                            <i class="fa fa-file-text"></i> 请假
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-user"></i> 张同学
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fa fa-user"></i> 个人信息</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fa fa-cog"></i> 设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../index.html"><i class="fa fa-sign-out"></i> 退出</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fa fa-calendar"></i> 我的课表</h2>
                <p class="text-muted">查看课程安排，合理规划学习时间</p>
            </div>
        </div>

        <!-- 视图切换 -->
        <div class="row">
            <div class="col-12">
                <div class="view-toggle text-center">
                    <button class="btn btn-primary btn-toggle active" onclick="showWeekView()">
                        <i class="fa fa-calendar-week"></i> 周视图
                    </button>
                    <button class="btn btn-outline-primary btn-toggle" onclick="showDayView()">
                        <i class="fa fa-calendar-day"></i> 日视图
                    </button>
                </div>
            </div>
        </div>

        <!-- 周导航 -->
        <div class="week-navigation">
            <button class="week-nav-btn" onclick="previousWeek()">
                <i class="fa fa-chevron-left"></i>
            </button>
            <div class="current-week" id="currentWeek">
                2025年1月第3周 (1月20日 - 1月26日)
            </div>
            <button class="week-nav-btn" onclick="nextWeek()">
                <i class="fa fa-chevron-right"></i>
            </button>
        </div>

        <!-- 周视图课表 -->
        <div class="row" id="weekView">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-calendar-week"></i> 周课表</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table schedule-table mb-0">
                                <thead>
                                    <tr>
                                        <th style="width: 100px;">时间</th>
                                        <th>周一</th>
                                        <th>周二</th>
                                        <th>周三</th>
                                        <th>周四</th>
                                        <th>周五</th>
                                        <th>周六</th>
                                        <th>周日</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="time-slot">09:00<br>10:30</td>
                                        <td>
                                            <div class="course-item" onclick="showCourseDetail('数学', '李老师', 'A101')">
                                                <div class="course-subject">数学</div>
                                                <div class="course-teacher">李老师</div>
                                                <div class="course-room">A101</div>
                                            </div>
                                        </td>
                                        <td></td>
                                        <td>
                                            <div class="course-item" onclick="showCourseDetail('物理', '张老师', 'C301')">
                                                <div class="course-subject">物理</div>
                                                <div class="course-teacher">张老师</div>
                                                <div class="course-room">C301</div>
                                            </div>
                                        </td>
                                        <td></td>
                                        <td>
                                            <div class="course-item" onclick="showCourseDetail('数学', '李老师', 'A101')">
                                                <div class="course-subject">数学</div>
                                                <div class="course-teacher">李老师</div>
                                                <div class="course-room">A101</div>
                                            </div>
                                        </td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td class="time-slot">14:00<br>15:30</td>
                                        <td></td>
                                        <td>
                                            <div class="course-item" onclick="showCourseDetail('英语', '王老师', 'B203')">
                                                <div class="course-subject">英语</div>
                                                <div class="course-teacher">王老师</div>
                                                <div class="course-room">B203</div>
                                            </div>
                                        </td>
                                        <td></td>
                                        <td>
                                            <div class="course-item" onclick="showCourseDetail('英语', '王老师', 'B203')">
                                                <div class="course-subject">英语</div>
                                                <div class="course-teacher">王老师</div>
                                                <div class="course-room">B203</div>
                                            </div>
                                        </td>
                                        <td></td>
                                        <td>
                                            <div class="course-item" onclick="showCourseDetail('化学', '赵老师', 'D401')">
                                                <div class="course-subject">化学</div>
                                                <div class="course-teacher">赵老师</div>
                                                <div class="course-room">D401</div>
                                            </div>
                                        </td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td class="time-slot">16:00<br>17:30</td>
                                        <td>
                                            <div class="course-item" onclick="showCourseDetail('物理', '张老师', 'C301')">
                                                <div class="course-subject">物理</div>
                                                <div class="course-teacher">张老师</div>
                                                <div class="course-room">C301</div>
                                            </div>
                                        </td>
                                        <td></td>
                                        <td>
                                            <div class="course-item" onclick="showCourseDetail('化学', '赵老师', 'D401')">
                                                <div class="course-subject">化学</div>
                                                <div class="course-teacher">赵老师</div>
                                                <div class="course-room">D401</div>
                                            </div>
                                        </td>
                                        <td></td>
                                        <td>
                                            <div class="course-item" onclick="showCourseDetail('英语', '王老师', 'B203')">
                                                <div class="course-subject">英语</div>
                                                <div class="course-teacher">王老师</div>
                                                <div class="course-room">B203</div>
                                            </div>
                                        </td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日视图课表 -->
        <div class="row" id="dayView" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-calendar-day"></i> 今日课程 - <span id="currentDate"></span></h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card border-primary">
                                    <div class="card-body">
                                        <h6 class="card-title text-primary">09:00 - 10:30</h6>
                                        <h5>数学 - 代数基础</h5>
                                        <p class="card-text">
                                            <i class="fa fa-user"></i> 李老师<br>
                                            <i class="fa fa-map-marker"></i> 教室A101
                                        </p>
                                        <button class="btn btn-sm btn-outline-primary" onclick="showCourseDetail('数学', '李老师', 'A101')">
                                            查看详情
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card border-success">
                                    <div class="card-body">
                                        <h6 class="card-title text-success">14:00 - 15:30</h6>
                                        <h5>英语 - 语法练习</h5>
                                        <p class="card-text">
                                            <i class="fa fa-user"></i> 王老师<br>
                                            <i class="fa fa-map-marker"></i> 教室B203
                                        </p>
                                        <button class="btn btn-sm btn-outline-success" onclick="showCourseDetail('英语', '王老师', 'B203')">
                                            查看详情
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card border-warning">
                                    <div class="card-body">
                                        <h6 class="card-title text-warning">16:00 - 17:30</h6>
                                        <h5>物理 - 力学实验</h5>
                                        <p class="card-text">
                                            <i class="fa fa-user"></i> 张老师<br>
                                            <i class="fa fa-map-marker"></i> 实验室C301
                                        </p>
                                        <button class="btn btn-sm btn-outline-warning" onclick="showCourseDetail('物理', '张老师', 'C301')">
                                            查看详情
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 课程详情模态框 -->
    <div class="modal fade" id="courseDetailModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="courseModalTitle">课程详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="courseModalBody">
                    <!-- 课程详情内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="location.href='leave.html'">申请请假</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../web/js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../web/bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 显示当前日期
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });

        // 视图切换
        function showWeekView() {
            document.getElementById('weekView').style.display = 'block';
            document.getElementById('dayView').style.display = 'none';
            document.querySelector('.btn-toggle.active').classList.remove('active');
            document.querySelector('.btn-toggle.active').classList.add('btn-outline-primary');
            document.querySelector('.btn-toggle.active').classList.remove('btn-primary');
            event.target.classList.add('active');
            event.target.classList.add('btn-primary');
            event.target.classList.remove('btn-outline-primary');
        }

        function showDayView() {
            document.getElementById('weekView').style.display = 'none';
            document.getElementById('dayView').style.display = 'block';
            document.querySelector('.btn-toggle.active').classList.remove('active');
            document.querySelector('.btn-toggle.active').classList.add('btn-outline-primary');
            document.querySelector('.btn-toggle.active').classList.remove('btn-primary');
            event.target.classList.add('active');
            event.target.classList.add('btn-primary');
            event.target.classList.remove('btn-outline-primary');
        }

        // 周导航
        function previousWeek() {
            // 这里应该实现上一周的逻辑
            console.log('切换到上一周');
        }

        function nextWeek() {
            // 这里应该实现下一周的逻辑
            console.log('切换到下一周');
        }

        // 显示课程详情
        function showCourseDetail(subject, teacher, room) {
            document.getElementById('courseModalTitle').textContent = subject + ' - 课程详情';
            document.getElementById('courseModalBody').innerHTML = `
                <div class="row">
                    <div class="col-6"><strong>课程名称：</strong></div>
                    <div class="col-6">${subject}</div>
                </div>
                <div class="row mt-2">
                    <div class="col-6"><strong>授课教师：</strong></div>
                    <div class="col-6">${teacher}</div>
                </div>
                <div class="row mt-2">
                    <div class="col-6"><strong>上课地点：</strong></div>
                    <div class="col-6">${room}</div>
                </div>
                <div class="row mt-2">
                    <div class="col-6"><strong>课程时长：</strong></div>
                    <div class="col-6">90分钟</div>
                </div>
                <div class="row mt-2">
                    <div class="col-12"><strong>课程描述：</strong></div>
                    <div class="col-12 mt-1">本节课将学习${subject}相关知识点，请同学们提前预习相关内容。</div>
                </div>
            `;
            new bootstrap.Modal(document.getElementById('courseDetailModal')).show();
        }
    </script>
</body>
</html>
