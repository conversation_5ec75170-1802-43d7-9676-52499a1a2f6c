<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>家长主页 - 特靠谱教培系统</title>
    <!-- Bootstrap CSS -->
    <link href="../../web/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../web/css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .student-card {
            border-left: 4px solid #ff6b6b;
            margin-bottom: 20px;
        }
        .student-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }
        .relation-badge {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
        }
        .schedule-item {
            border-left: 4px solid #ff6b6b;
            padding: 15px;
            margin-bottom: 10px;
            background-color: #fff;
            border-radius: 0 10px 10px 0;
        }
        .btn-parent {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            border: none;
            border-radius: 25px;
            padding: 8px 20px;
            color: white;
            transition: all 0.3s;
        }
        .btn-parent:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
            color: white;
        }
        .stats-card {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
        }
        .attendance-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        .status-present {
            background-color: #d1edff;
            color: #0c5460;
        }
        .status-absent {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status-leave {
            background-color: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);">
        <div class="container">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fa fa-graduation-cap me-2"></i>特靠谱教培系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.html">
                            <i class="fa fa-home me-1"></i>主页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="schedule.html">
                            <i class="fa fa-calendar me-1"></i>课表
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.html">
                            <i class="fa fa-check-circle me-1"></i>出勤记录
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="leave.html">
                            <i class="fa fa-file-text me-1"></i>请假记录
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-user me-1"></i>李妈妈
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fa fa-user me-2"></i>个人资料</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fa fa-cog me-2"></i>设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../index.html"><i class="fa fa-sign-out me-2"></i>退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 欢迎信息 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h4 class="mb-1">欢迎您，李妈妈！</h4>
                                <p class="text-muted mb-0">
                                    <span class="relation-badge me-2">母亲</span>
                                    今天是 2025年8月21日，您可以查看孩子的学习情况
                                </p>
                            </div>
                            <div class="col-md-4 text-end">
                                <button class="btn btn-parent" data-bs-toggle="modal" data-bs-target="#switchChildModal">
                                    <i class="fa fa-exchange me-2"></i>切换孩子
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 当前查看的学生信息 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="student-card card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-user me-2"></i>当前查看学生</h5>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <div class="student-avatar">李</div>
                            </div>
                            <div class="col-md-6">
                                <h5 class="mb-1">李小明</h5>
                                <p class="text-muted mb-1">学号：2024001</p>
                                <p class="text-muted mb-1">班级：高一(1)班</p>
                                <p class="text-muted mb-0">班主任：张老师</p>
                            </div>
                            <div class="col-md-4">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="stats-card">
                                            <div class="stats-number">95%</div>
                                            <div>出勤率</div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="stats-card">
                                            <div class="stats-number">2</div>
                                            <div>本月请假</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 今日课程和出勤 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-calendar me-2"></i>今日课程</h5>
                    </div>
                    <div class="card-body">
                        <div class="schedule-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">数学</h6>
                                    <small class="text-muted">08:00 - 09:40 | 教室A101</small>
                                </div>
                                <span class="attendance-status status-present">已出勤</span>
                            </div>
                        </div>
                        <div class="schedule-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">语文</h6>
                                    <small class="text-muted">10:00 - 11:40 | 教室A101</small>
                                </div>
                                <span class="attendance-status status-present">已出勤</span>
                            </div>
                        </div>
                        <div class="schedule-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">英语</h6>
                                    <small class="text-muted">14:00 - 15:40 | 教室A101</small>
                                </div>
                                <span class="attendance-status status-leave">请假</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-file-text me-2"></i>最近请假记录</h5>
                    </div>
                    <div class="card-body">
                        <div class="schedule-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">病假</h6>
                                    <small class="text-muted">2025-08-21 英语课</small>
                                </div>
                                <span class="badge bg-warning">审批中</span>
                            </div>
                        </div>
                        <div class="schedule-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">事假</h6>
                                    <small class="text-muted">2025-08-19 体育课</small>
                                </div>
                                <span class="badge bg-success">已批准</span>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <a href="leave.html" class="btn btn-parent btn-sm">
                                <i class="fa fa-plus me-1"></i>申请请假
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 本周出勤统计 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-chart-bar me-2"></i>本周出勤统计</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-2">
                                <div class="mb-2">
                                    <strong>周一</strong>
                                </div>
                                <div class="attendance-status status-present">全勤</div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-2">
                                    <strong>周二</strong>
                                </div>
                                <div class="attendance-status status-present">全勤</div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-2">
                                    <strong>周三</strong>
                                </div>
                                <div class="attendance-status status-leave">请假1节</div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-2">
                                    <strong>周四</strong>
                                </div>
                                <div class="attendance-status status-present">全勤</div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-2">
                                    <strong>周五</strong>
                                </div>
                                <div class="attendance-status status-present">全勤</div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-2">
                                    <strong>本周</strong>
                                </div>
                                <div class="stats-card">
                                    <div class="stats-number" style="font-size: 1.2rem;">95%</div>
                                    <div style="font-size: 0.8rem;">出勤率</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="row">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fa fa-calendar text-primary" style="font-size: 3rem;"></i>
                        <h6 class="mt-3">查看课表</h6>
                        <a href="schedule.html" class="btn btn-parent btn-sm">进入</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fa fa-check-circle text-success" style="font-size: 3rem;"></i>
                        <h6 class="mt-3">出勤记录</h6>
                        <a href="attendance.html" class="btn btn-parent btn-sm">进入</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fa fa-file-text text-warning" style="font-size: 3rem;"></i>
                        <h6 class="mt-3">请假申请</h6>
                        <a href="leave.html" class="btn btn-parent btn-sm">进入</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fa fa-phone text-info" style="font-size: 3rem;"></i>
                        <h6 class="mt-3">联系老师</h6>
                        <button class="btn btn-parent btn-sm" onclick="contactTeacher()">联系</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 切换孩子模态框 -->
    <div class="modal fade" id="switchChildModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%); color: white;">
                    <h5 class="modal-title">选择要查看的孩子</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="list-group">
                        <a href="#" class="list-group-item list-group-item-action active" onclick="switchChild('李小明')">
                            <div class="d-flex align-items-center">
                                <div class="student-avatar me-3" style="width: 40px; height: 40px; font-size: 1rem;">李</div>
                                <div>
                                    <h6 class="mb-1">李小明</h6>
                                    <small class="text-muted">学号：2024001 | 高一(1)班</small>
                                </div>
                            </div>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action" onclick="switchChild('李小红')">
                            <div class="d-flex align-items-center">
                                <div class="student-avatar me-3" style="width: 40px; height: 40px; font-size: 1rem;">红</div>
                                <div>
                                    <h6 class="mb-1">李小红</h6>
                                    <small class="text-muted">学号：2024005 | 初三(2)班</small>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="../../web/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="../../web/js/jquery.min.js"></script>

    <script>
        // 切换孩子 - 修订时间：2025-08-21 23:45:48
        function switchChild(childName) {
            alert('切换到查看 ' + childName + ' 的信息');
            const modal = bootstrap.Modal.getInstance(document.getElementById('switchChildModal'));
            modal.hide();
        }

        // 联系老师 - 修订时间：2025-08-21 23:45:48
        function contactTeacher() {
            alert('联系班主任张老师\n电话：138-0000-0000\n微信：zhang_teacher');
        }

        // 页面加载时显示当前时间 - 修订时间：2025-08-21 23:45:48
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            const dateStr = now.getFullYear() + '年' + (now.getMonth() + 1) + '月' + now.getDate() + '日';
            // 可以在这里更新页面中的日期显示
        });
    </script>
</body>
</html>
