<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教学课表 - 特靠谱教培系统</title>
    <!-- Bootstrap CSS -->
    <link href="../../web/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../web/css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        .card-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .schedule-table {
            font-size: 0.9rem;
        }
        .schedule-table th {
            background-color: #f8f9fa;
            border: none;
            text-align: center;
            padding: 15px 8px;
            font-weight: 600;
        }
        .schedule-table td {
            border: 1px solid #e9ecef;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
            height: 100px;
        }
        .time-slot {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        .course-item {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 8px;
            padding: 10px;
            margin: 2px 0;
            font-size: 0.8rem;
            line-height: 1.3;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .course-item:hover {
            transform: scale(1.05);
        }
        .course-subject {
            font-weight: bold;
            margin-bottom: 3px;
        }
        .course-students {
            font-size: 0.7rem;
            opacity: 0.9;
        }
        .course-room {
            font-size: 0.7rem;
            opacity: 0.8;
        }
        .view-toggle {
            margin-bottom: 20px;
        }
        .btn-toggle {
            border-radius: 25px;
            padding: 8px 20px;
            margin: 0 5px;
        }
        .week-navigation {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }
        .week-nav-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #28a745;
            padding: 10px;
            cursor: pointer;
        }
        .week-nav-btn:hover {
            color: #20c997;
        }
        .current-week {
            margin: 0 20px;
            font-weight: bold;
            color: #495057;
        }
        .attendance-info {
            font-size: 0.7rem;
            margin-top: 2px;
        }
        .attendance-rate {
            color: #ffc107;
        }
        @media (max-width: 768px) {
            .schedule-table {
                font-size: 0.7rem;
            }
            .schedule-table td {
                height: 80px;
                padding: 4px;
            }
            .course-item {
                padding: 6px;
                font-size: 0.7rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fa fa-home"></i> 主页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="schedule.html">
                            <i class="fa fa-calendar"></i> 课表
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="leave-approval.html">
                            <i class="fa fa-file-text"></i> 请假审批
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-user"></i> 李老师
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fa fa-user"></i> 个人信息</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fa fa-cog"></i> 设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../index.html"><i class="fa fa-sign-out"></i> 退出</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fa fa-calendar"></i> 教学课表</h2>
                <p class="text-muted">管理课程安排，查看学生出勤情况</p>
            </div>
        </div>

        <!-- 视图切换 -->
        <div class="row">
            <div class="col-12">
                <div class="view-toggle text-center">
                    <button class="btn btn-success btn-toggle active" onclick="showWeekView()">
                        <i class="fa fa-calendar-week"></i> 周视图
                    </button>
                    <button class="btn btn-outline-success btn-toggle" onclick="showDayView()">
                        <i class="fa fa-calendar-day"></i> 日视图
                    </button>
                </div>
            </div>
        </div>

        <!-- 周导航 -->
        <div class="week-navigation">
            <button class="week-nav-btn" onclick="previousWeek()">
                <i class="fa fa-chevron-left"></i>
            </button>
            <div class="current-week" id="currentWeek">
                2025年1月第3周 (1月20日 - 1月26日)
            </div>
            <button class="week-nav-btn" onclick="nextWeek()">
                <i class="fa fa-chevron-right"></i>
            </button>
        </div>

        <!-- 周视图课表 -->
        <div class="row" id="weekView">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-calendar-week"></i> 周课表</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table schedule-table mb-0">
                                <thead>
                                    <tr>
                                        <th style="width: 100px;">时间</th>
                                        <th>周一</th>
                                        <th>周二</th>
                                        <th>周三</th>
                                        <th>周四</th>
                                        <th>周五</th>
                                        <th>周六</th>
                                        <th>周日</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="time-slot">09:00<br>10:30</td>
                                        <td>
                                            <div class="course-item" onclick="showCourseDetail('数学-代数基础', '12名学生', 'A101', '95%')">
                                                <div class="course-subject">数学-代数基础</div>
                                                <div class="course-students">12名学生</div>
                                                <div class="course-room">教室A101</div>
                                                <div class="attendance-info">
                                                    <span class="attendance-rate">出勤率: 95%</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td></td>
                                        <td>
                                            <div class="course-item" onclick="showCourseDetail('数学-几何基础', '8名学生', 'A102', '88%')">
                                                <div class="course-subject">数学-几何基础</div>
                                                <div class="course-students">8名学生</div>
                                                <div class="course-room">教室A102</div>
                                                <div class="attendance-info">
                                                    <span class="attendance-rate">出勤率: 88%</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td></td>
                                        <td>
                                            <div class="course-item" onclick="showCourseDetail('数学-代数基础', '12名学生', 'A101', '92%')">
                                                <div class="course-subject">数学-代数基础</div>
                                                <div class="course-students">12名学生</div>
                                                <div class="course-room">教室A101</div>
                                                <div class="attendance-info">
                                                    <span class="attendance-rate">出勤率: 92%</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td class="time-slot">14:00<br>15:30</td>
                                        <td></td>
                                        <td>
                                            <div class="course-item" onclick="showCourseDetail('数学-综合练习', '15名学生', 'A101', '90%')">
                                                <div class="course-subject">数学-综合练习</div>
                                                <div class="course-students">15名学生</div>
                                                <div class="course-room">教室A101</div>
                                                <div class="attendance-info">
                                                    <span class="attendance-rate">出勤率: 90%</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td></td>
                                        <td>
                                            <div class="course-item" onclick="showCourseDetail('数学-几何基础', '8名学生', 'A102', '100%')">
                                                <div class="course-subject">数学-几何基础</div>
                                                <div class="course-students">8名学生</div>
                                                <div class="course-room">教室A102</div>
                                                <div class="attendance-info">
                                                    <span class="attendance-rate">出勤率: 100%</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td></td>
                                        <td>
                                            <div class="course-item" onclick="showCourseDetail('数学-应用题', '10名学生', 'A103', '85%')">
                                                <div class="course-subject">数学-应用题</div>
                                                <div class="course-students">10名学生</div>
                                                <div class="course-room">教室A103</div>
                                                <div class="attendance-info">
                                                    <span class="attendance-rate">出勤率: 85%</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td class="time-slot">16:00<br>17:30</td>
                                        <td>
                                            <div class="course-item" onclick="showCourseDetail('数学-综合练习', '15名学生', 'A101', '93%')">
                                                <div class="course-subject">数学-综合练习</div>
                                                <div class="course-students">15名学生</div>
                                                <div class="course-room">教室A101</div>
                                                <div class="attendance-info">
                                                    <span class="attendance-rate">出勤率: 93%</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td></td>
                                        <td>
                                            <div class="course-item" onclick="showCourseDetail('数学-应用题', '10名学生', 'A103', '80%')">
                                                <div class="course-subject">数学-应用题</div>
                                                <div class="course-students">10名学生</div>
                                                <div class="course-room">教室A103</div>
                                                <div class="attendance-info">
                                                    <span class="attendance-rate">出勤率: 80%</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td></td>
                                        <td>
                                            <div class="course-item" onclick="showCourseDetail('数学-代数基础', '12名学生', 'A101', '96%')">
                                                <div class="course-subject">数学-代数基础</div>
                                                <div class="course-students">12名学生</div>
                                                <div class="course-room">教室A101</div>
                                                <div class="attendance-info">
                                                    <span class="attendance-rate">出勤率: 96%</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../web/js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../web/bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 视图切换
        function showWeekView() {
            // 实现周视图切换逻辑
            console.log('切换到周视图');
        }

        function showDayView() {
            // 实现日视图切换逻辑
            console.log('切换到日视图');
        }

        // 周导航
        function previousWeek() {
            console.log('切换到上一周');
        }

        function nextWeek() {
            console.log('切换到下一周');
        }

        // 显示课程详情
        function showCourseDetail(subject, students, room, attendance) {
            alert(`课程：${subject}\n学生：${students}\n教室：${room}\n出勤率：${attendance}`);
        }
    </script>
</body>
</html>
