<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课程管理 - 特靠谱教培系统</title>
    <!-- Bootstrap CSS -->
    <link href="../../web/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../web/css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        .card-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .course-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            background: white;
            transition: all 0.3s;
        }
        .course-card:hover {
            border-color: #6f42c1;
            box-shadow: 0 4px 12px rgba(111, 66, 193, 0.15);
        }
        .course-status {
            font-size: 0.8rem;
            padding: 4px 8px;
            border-radius: 12px;
        }
        .status-active {
            background-color: #d1edff;
            color: #0c5460;
        }
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status-full {
            background-color: #fff3cd;
            color: #856404;
        }
        .filter-tabs {
            margin-bottom: 20px;
        }
        .filter-tab {
            border: none;
            background: none;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 25px;
            color: #6c757d;
            transition: all 0.3s;
        }
        .filter-tab.active {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
        }
        .search-box {
            margin-bottom: 20px;
        }
        .btn-add-course {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            border: none;
            color: white;
            border-radius: 25px;
            padding: 10px 25px;
            transition: transform 0.2s;
        }
        .btn-add-course:hover {
            transform: translateY(-2px);
            color: white;
        }
        .course-info {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .course-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-right: 15px;
        }
        .course-details {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
        }
        .course-actions {
            border-top: 1px solid #e9ecef;
            padding-top: 15px;
            margin-top: 15px;
        }
        .btn-action {
            padding: 5px 15px;
            font-size: 0.8rem;
            border-radius: 15px;
            margin: 0 5px;
        }
        .stats-card {
            text-align: center;
            padding: 20px;
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #6f42c1;
        }
        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .schedule-time {
            background-color: #e9ecef;
            border-radius: 5px;
            padding: 5px 10px;
            font-size: 0.8rem;
            margin: 2px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fa fa-home"></i> 主页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="users.html">
                            <i class="fa fa-users"></i> 用户管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="courses.html">
                            <i class="fa fa-book"></i> 课程管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classrooms.html">
                            <i class="fa fa-building"></i> 教室管理
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-user-shield"></i> 管理员
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fa fa-user"></i> 个人信息</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fa fa-cog"></i> 系统设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../index.html"><i class="fa fa-sign-out"></i> 退出</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-md-8">
                <h2><i class="fa fa-book"></i> 课程管理</h2>
                <p class="text-muted">创建和管理课程，安排教师和教室</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-add-course" data-bs-toggle="modal" data-bs-target="#addCourseModal">
                    <i class="fa fa-plus"></i> 创建课程
                </button>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card">
                    <div class="card-body stats-card">
                        <div class="stats-number">45</div>
                        <div class="stats-label">课程总数</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card">
                    <div class="card-body stats-card">
                        <div class="stats-number">38</div>
                        <div class="stats-label">进行中</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card">
                    <div class="card-body stats-card">
                        <div class="stats-number">12</div>
                        <div class="stats-label">课程冲突</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card">
                    <div class="card-body stats-card">
                        <div class="stats-number">92%</div>
                        <div class="stats-label">平均出勤率</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="search-box">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="搜索课程名称或教师..." id="searchInput">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fa fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="filter-tabs text-end">
                    <button class="filter-tab active" onclick="filterCourses('all')">全部</button>
                    <button class="filter-tab" onclick="filterCourses('active')">进行中</button>
                    <button class="filter-tab" onclick="filterCourses('full')">已满员</button>
                    <button class="filter-tab" onclick="filterCourses('inactive')">已结束</button>
                </div>
            </div>
        </div>

        <!-- 课程列表 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-list"></i> 课程列表</h5>
                    </div>
                    <div class="card-body">
                        <!-- 数学课程 -->
                        <div class="course-card" data-status="active">
                            <div class="course-info">
                                <div class="course-icon">
                                    <i class="fa fa-calculator"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">数学 - 代数基础</h6>
                                    <small class="text-muted">课程编号：MATH001</small>
                                </div>
                                <div class="ms-auto">
                                    <span class="course-status status-active">进行中</span>
                                </div>
                            </div>
                            
                            <div class="course-details">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>授课教师：</strong>李老师
                                    </div>
                                    <div class="col-md-3">
                                        <strong>学生人数：</strong>12/15人
                                    </div>
                                    <div class="col-md-3">
                                        <strong>课程时长：</strong>90分钟
                                    </div>
                                    <div class="col-md-3">
                                        <strong>教室：</strong>A101
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <strong>上课时间：</strong>
                                        <span class="schedule-time">周一 09:00-10:30</span>
                                        <span class="schedule-time">周三 09:00-10:30</span>
                                        <span class="schedule-time">周五 09:00-10:30</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="course-actions">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <small class="text-muted">创建时间：2025-01-15 | 最后更新：2025-01-20</small>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <button class="btn btn-outline-primary btn-action" onclick="editCourse('数学-代数基础')">
                                            <i class="fa fa-edit"></i> 编辑
                                        </button>
                                        <button class="btn btn-outline-success btn-action" onclick="viewStudents('数学-代数基础')">
                                            <i class="fa fa-users"></i> 学生
                                        </button>
                                        <button class="btn btn-outline-danger btn-action" onclick="deleteCourse('数学-代数基础')">
                                            <i class="fa fa-trash"></i> 删除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 英语课程 -->
                        <div class="course-card" data-status="full">
                            <div class="course-info">
                                <div class="course-icon">
                                    <i class="fa fa-language"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">英语 - 语法练习</h6>
                                    <small class="text-muted">课程编号：ENG001</small>
                                </div>
                                <div class="ms-auto">
                                    <span class="course-status status-full">已满员</span>
                                </div>
                            </div>
                            
                            <div class="course-details">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>授课教师：</strong>王老师
                                    </div>
                                    <div class="col-md-3">
                                        <strong>学生人数：</strong>20/20人
                                    </div>
                                    <div class="col-md-3">
                                        <strong>课程时长：</strong>90分钟
                                    </div>
                                    <div class="col-md-3">
                                        <strong>教室：</strong>B203
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <strong>上课时间：</strong>
                                        <span class="schedule-time">周二 14:00-15:30</span>
                                        <span class="schedule-time">周四 14:00-15:30</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="course-actions">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <small class="text-muted">创建时间：2025-01-10 | 最后更新：2025-01-18</small>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <button class="btn btn-outline-primary btn-action" onclick="editCourse('英语-语法练习')">
                                            <i class="fa fa-edit"></i> 编辑
                                        </button>
                                        <button class="btn btn-outline-success btn-action" onclick="viewStudents('英语-语法练习')">
                                            <i class="fa fa-users"></i> 学生
                                        </button>
                                        <button class="btn btn-outline-danger btn-action" onclick="deleteCourse('英语-语法练习')">
                                            <i class="fa fa-trash"></i> 删除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 物理课程 -->
                        <div class="course-card" data-status="inactive">
                            <div class="course-info">
                                <div class="course-icon">
                                    <i class="fa fa-atom"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">物理 - 力学实验</h6>
                                    <small class="text-muted">课程编号：PHY001</small>
                                </div>
                                <div class="ms-auto">
                                    <span class="course-status status-inactive">已结束</span>
                                </div>
                            </div>
                            
                            <div class="course-details">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>授课教师：</strong>张老师
                                    </div>
                                    <div class="col-md-3">
                                        <strong>学生人数：</strong>15/18人
                                    </div>
                                    <div class="col-md-3">
                                        <strong>课程时长：</strong>90分钟
                                    </div>
                                    <div class="col-md-3">
                                        <strong>教室：</strong>C301
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <strong>上课时间：</strong>
                                        <span class="schedule-time">周一 16:00-17:30</span>
                                        <span class="schedule-time">周三 16:00-17:30</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="course-actions">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <small class="text-muted">创建时间：2024-12-01 | 结束时间：2025-01-15</small>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <button class="btn btn-outline-warning btn-action" onclick="reactivateCourse('物理-力学实验')">
                                            <i class="fa fa-play"></i> 重新开课
                                        </button>
                                        <button class="btn btn-outline-info btn-action" onclick="viewStudents('物理-力学实验')">
                                            <i class="fa fa-users"></i> 学生
                                        </button>
                                        <button class="btn btn-outline-danger btn-action" onclick="deleteCourse('物理-力学实验')">
                                            <i class="fa fa-trash"></i> 删除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建课程模态框 -->
    <div class="modal fade" id="addCourseModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建课程</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addCourseForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">课程名称</label>
                                <input type="text" class="form-control" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">课程编号</label>
                                <input type="text" class="form-control" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">授课教师</label>
                                <select class="form-select" required>
                                    <option value="">请选择教师</option>
                                    <option value="李老师">李老师</option>
                                    <option value="王老师">王老师</option>
                                    <option value="张老师">张老师</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">教室</label>
                                <select class="form-select" required>
                                    <option value="">请选择教室</option>
                                    <option value="A101">A101</option>
                                    <option value="A102">A102</option>
                                    <option value="B203">B203</option>
                                    <option value="C301">C301</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">课程时长（分钟）</label>
                                <input type="number" class="form-control" value="90" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">最大学生数</label>
                                <input type="number" class="form-control" value="20" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">课程描述</label>
                            <textarea class="form-control" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">上课时间</label>
                            <div class="row">
                                <div class="col-md-4">
                                    <select class="form-select mb-2">
                                        <option value="">选择星期</option>
                                        <option value="1">周一</option>
                                        <option value="2">周二</option>
                                        <option value="3">周三</option>
                                        <option value="4">周四</option>
                                        <option value="5">周五</option>
                                        <option value="6">周六</option>
                                        <option value="7">周日</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <input type="time" class="form-control mb-2" value="09:00">
                                </div>
                                <div class="col-md-4">
                                    <button type="button" class="btn btn-outline-primary mb-2">
                                        <i class="fa fa-plus"></i> 添加时间
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="addCourse()">创建课程</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../web/js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../web/bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 筛选课程
        function filterCourses(status) {
            // 更新筛选按钮状态
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 筛选课程列表
            const courses = document.querySelectorAll('.course-card');
            courses.forEach(course => {
                if (status === 'all' || course.dataset.status === status) {
                    course.style.display = 'block';
                } else {
                    course.style.display = 'none';
                }
            });
        }

        // 编辑课程
        function editCourse(name) {
            alert(`编辑课程：${name}`);
        }

        // 查看学生
        function viewStudents(name) {
            alert(`查看课程 ${name} 的学生列表`);
        }

        // 删除课程
        function deleteCourse(name) {
            if (confirm(`确认删除课程：${name}？`)) {
                alert(`课程 ${name} 已删除`);
            }
        }

        // 重新开课
        function reactivateCourse(name) {
            if (confirm(`确认重新开设课程：${name}？`)) {
                alert(`课程 ${name} 已重新开设`);
            }
        }

        // 创建课程
        function addCourse() {
            alert('课程创建成功');
            bootstrap.Modal.getInstance(document.getElementById('addCourseModal')).hide();
        }

        // 搜索功能
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const courses = document.querySelectorAll('.course-card');
            
            courses.forEach(course => {
                const text = course.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    course.style.display = 'block';
                } else {
                    course.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
