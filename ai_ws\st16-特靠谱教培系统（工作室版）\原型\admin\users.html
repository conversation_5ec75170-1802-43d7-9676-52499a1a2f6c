<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 特靠谱教培系统</title>
    <!-- Bootstrap CSS -->
    <link href="../../web/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../web/css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        .card-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .user-status {
            font-size: 0.8rem;
            padding: 4px 8px;
            border-radius: 12px;
        }
        .status-active {
            background-color: #d1edff;
            color: #0c5460;
        }
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        .filter-tabs {
            margin-bottom: 20px;
        }
        .filter-tab {
            border: none;
            background: none;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 25px;
            color: #6c757d;
            transition: all 0.3s;
        }
        .filter-tab.active {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
        }
        .search-box {
            margin-bottom: 20px;
        }
        .btn-add-user {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            border: none;
            color: white;
            border-radius: 25px;
            padding: 10px 25px;
            transition: transform 0.2s;
        }
        .btn-add-user:hover {
            transform: translateY(-2px);
            color: white;
        }
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }
        .table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
        }
        .table td {
            border: none;
            vertical-align: middle;
        }
        .table tbody tr {
            border-bottom: 1px solid #e9ecef;
        }
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        .btn-action {
            padding: 5px 10px;
            font-size: 0.8rem;
            border-radius: 15px;
            margin: 0 2px;
        }
        .stats-card {
            text-align: center;
            padding: 20px;
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #6f42c1;
        }
        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fa fa-home"></i> 主页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="users.html">
                            <i class="fa fa-users"></i> 用户管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.html">
                            <i class="fa fa-book"></i> 课程管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classrooms.html">
                            <i class="fa fa-building"></i> 教室管理
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-user-shield"></i> 管理员
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fa fa-user"></i> 个人信息</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fa fa-cog"></i> 系统设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../index.html"><i class="fa fa-sign-out"></i> 退出</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-md-8">
                <h2><i class="fa fa-users"></i> 用户管理</h2>
                <p class="text-muted">管理学生和教师账户信息</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-add-user" data-bs-toggle="modal" data-bs-target="#addUserModal">
                    <i class="fa fa-plus"></i> 添加用户
                </button>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card">
                    <div class="card-body stats-card">
                        <div class="stats-number">156</div>
                        <div class="stats-label">学生总数</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card">
                    <div class="card-body stats-card">
                        <div class="stats-number">12</div>
                        <div class="stats-label">教师总数</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card">
                    <div class="card-body stats-card">
                        <div class="stats-number">5</div>
                        <div class="stats-label">待审核</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card">
                    <div class="card-body stats-card">
                        <div class="stats-number">145</div>
                        <div class="stats-label">活跃用户</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="search-box">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="搜索用户姓名、邮箱或手机号..." id="searchInput">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fa fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="filter-tabs text-end">
                    <button class="filter-tab active" onclick="filterUsers('all')">全部</button>
                    <button class="filter-tab" onclick="filterUsers('student')">学生</button>
                    <button class="filter-tab" onclick="filterUsers('teacher')">教师</button>
                    <button class="filter-tab" onclick="filterUsers('pending')">待审核</button>
                </div>
            </div>
        </div>

        <!-- 用户列表 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-list"></i> 用户列表</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table mb-0">
                                <thead>
                                    <tr>
                                        <th>用户</th>
                                        <th>角色</th>
                                        <th>联系方式</th>
                                        <th>注册时间</th>
                                        <th>状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr data-role="teacher" data-status="active">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="user-avatar">李</div>
                                                <div class="ms-3">
                                                    <strong>李老师</strong><br>
                                                    <small class="text-muted"><EMAIL></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="badge bg-success">教师</span></td>
                                        <td>138****1234</td>
                                        <td>2025-01-15</td>
                                        <td><span class="user-status status-active">正常</span></td>
                                        <td>
                                            <button class="btn btn-outline-primary btn-action" onclick="editUser('李老师')">
                                                <i class="fa fa-edit"></i> 编辑
                                            </button>
                                            <button class="btn btn-outline-danger btn-action" onclick="deleteUser('李老师')">
                                                <i class="fa fa-trash"></i> 删除
                                            </button>
                                        </td>
                                    </tr>
                                    <tr data-role="student" data-status="active">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="user-avatar">张</div>
                                                <div class="ms-3">
                                                    <strong>张同学</strong><br>
                                                    <small class="text-muted"><EMAIL></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="badge bg-primary">学生</span></td>
                                        <td>139****5678</td>
                                        <td>2025-01-18</td>
                                        <td><span class="user-status status-active">正常</span></td>
                                        <td>
                                            <button class="btn btn-outline-primary btn-action" onclick="editUser('张同学')">
                                                <i class="fa fa-edit"></i> 编辑
                                            </button>
                                            <button class="btn btn-outline-danger btn-action" onclick="deleteUser('张同学')">
                                                <i class="fa fa-trash"></i> 删除
                                            </button>
                                        </td>
                                    </tr>
                                    <tr data-role="teacher" data-status="pending">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="user-avatar">王</div>
                                                <div class="ms-3">
                                                    <strong>王老师</strong><br>
                                                    <small class="text-muted"><EMAIL></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="badge bg-warning">教师</span></td>
                                        <td>137****9876</td>
                                        <td>2025-01-20</td>
                                        <td><span class="user-status status-pending">待审核</span></td>
                                        <td>
                                            <button class="btn btn-outline-success btn-action" onclick="approveUser('王老师')">
                                                <i class="fa fa-check"></i> 审核
                                            </button>
                                            <button class="btn btn-outline-danger btn-action" onclick="deleteUser('王老师')">
                                                <i class="fa fa-trash"></i> 删除
                                            </button>
                                        </td>
                                    </tr>
                                    <tr data-role="student" data-status="inactive">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="user-avatar">刘</div>
                                                <div class="ms-3">
                                                    <strong>刘同学</strong><br>
                                                    <small class="text-muted"><EMAIL></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="badge bg-secondary">学生</span></td>
                                        <td>136****4321</td>
                                        <td>2025-01-10</td>
                                        <td><span class="user-status status-inactive">已禁用</span></td>
                                        <td>
                                            <button class="btn btn-outline-warning btn-action" onclick="enableUser('刘同学')">
                                                <i class="fa fa-unlock"></i> 启用
                                            </button>
                                            <button class="btn btn-outline-danger btn-action" onclick="deleteUser('刘同学')">
                                                <i class="fa fa-trash"></i> 删除
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="row mt-4">
            <div class="col-12">
                <nav>
                    <ul class="pagination justify-content-center">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1">上一页</a>
                        </li>
                        <li class="page-item active">
                            <a class="page-link" href="#">1</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">2</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">3</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">下一页</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 添加用户模态框 -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <div class="mb-3">
                            <label class="form-label">用户角色</label>
                            <select class="form-select" required>
                                <option value="">请选择角色</option>
                                <option value="student">学生</option>
                                <option value="teacher">教师</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">姓名</label>
                            <input type="text" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">邮箱</label>
                            <input type="email" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">手机号</label>
                            <input type="tel" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">初始密码</label>
                            <input type="password" class="form-control" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="addUser()">添加用户</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../web/js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../web/bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 筛选用户
        function filterUsers(role) {
            // 更新筛选按钮状态
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 筛选用户列表
            const rows = document.querySelectorAll('tbody tr');
            rows.forEach(row => {
                if (role === 'all' || row.dataset.role === role || row.dataset.status === role) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // 编辑用户
        function editUser(name) {
            alert(`编辑用户：${name}`);
        }

        // 删除用户
        function deleteUser(name) {
            if (confirm(`确认删除用户：${name}？`)) {
                alert(`用户 ${name} 已删除`);
            }
        }

        // 审核用户
        function approveUser(name) {
            if (confirm(`确认审核通过用户：${name}？`)) {
                alert(`用户 ${name} 审核通过`);
            }
        }

        // 启用用户
        function enableUser(name) {
            if (confirm(`确认启用用户：${name}？`)) {
                alert(`用户 ${name} 已启用`);
            }
        }

        // 添加用户
        function addUser() {
            alert('用户添加成功');
            bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
        }

        // 搜索功能
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = document.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
