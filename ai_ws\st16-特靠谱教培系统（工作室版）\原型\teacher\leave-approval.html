<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>请假审批 - 特靠谱教培系统</title>
    <!-- Bootstrap CSS -->
    <link href="../../web/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../web/css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        .card-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .leave-request {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            background: white;
            transition: all 0.3s;
        }
        .leave-request:hover {
            border-color: #28a745;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.15);
        }
        .leave-status {
            font-size: 0.9rem;
            padding: 6px 12px;
            border-radius: 15px;
            font-weight: 500;
        }
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status-approved {
            background-color: #d1edff;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status-rejected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .btn-approve {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            border-radius: 20px;
            padding: 8px 20px;
            font-size: 0.9rem;
            transition: transform 0.2s;
        }
        .btn-approve:hover {
            transform: translateY(-2px);
            color: white;
        }
        .btn-reject {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;
            color: white;
            border-radius: 20px;
            padding: 8px 20px;
            font-size: 0.9rem;
            transition: transform 0.2s;
        }
        .btn-reject:hover {
            transform: translateY(-2px);
            color: white;
        }
        .filter-tabs {
            margin-bottom: 20px;
        }
        .filter-tab {
            border: none;
            background: none;
            padding: 10px 20px;
            margin: 0 5px;
            border-radius: 25px;
            color: #6c757d;
            transition: all 0.3s;
        }
        .filter-tab.active {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        .student-info {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .student-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 15px;
        }
        .course-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
        }
        .reason-text {
            background-color: #fff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            margin: 10px 0;
            font-style: italic;
        }
        .approval-actions {
            border-top: 1px solid #e9ecef;
            padding-top: 15px;
            margin-top: 15px;
        }
        .search-box {
            margin-bottom: 20px;
        }
        .stats-card {
            text-align: center;
            padding: 20px;
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #28a745;
        }
        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fa fa-home"></i> 主页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="schedule.html">
                            <i class="fa fa-calendar"></i> 课表
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="leave-approval.html">
                            <i class="fa fa-file-text"></i> 请假审批
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-user"></i> 李老师
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fa fa-user"></i> 个人信息</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fa fa-cog"></i> 设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../index.html"><i class="fa fa-sign-out"></i> 退出</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题 -->
        <div class="row mb-4">
            <div class="col-12">
                <h2><i class="fa fa-file-text"></i> 请假审批</h2>
                <p class="text-muted">处理学生请假申请，管理课程出勤</p>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-md-3 col-6 mb-3">
                <div class="card">
                    <div class="card-body stats-card">
                        <div class="stats-number">8</div>
                        <div class="stats-label">待审批</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="card">
                    <div class="card-body stats-card">
                        <div class="stats-number">25</div>
                        <div class="stats-label">本月已批准</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="card">
                    <div class="card-body stats-card">
                        <div class="stats-number">3</div>
                        <div class="stats-label">本月已拒绝</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="card">
                    <div class="card-body stats-card">
                        <div class="stats-number">92%</div>
                        <div class="stats-label">平均出勤率</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="search-box">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="搜索学生姓名或课程..." id="searchInput">
                        <button class="btn btn-outline-success" type="button">
                            <i class="fa fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="filter-tabs text-end">
                    <button class="filter-tab active" onclick="filterRequests('all')">全部</button>
                    <button class="filter-tab" onclick="filterRequests('pending')">待审批</button>
                    <button class="filter-tab" onclick="filterRequests('approved')">已批准</button>
                    <button class="filter-tab" onclick="filterRequests('rejected')">已拒绝</button>
                </div>
            </div>
        </div>

        <!-- 请假申请列表 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-list"></i> 请假申请列表</h5>
                    </div>
                    <div class="card-body">
                        <!-- 待审批申请 -->
                        <div class="leave-request" data-status="pending">
                            <div class="student-info">
                                <div class="student-avatar">张</div>
                                <div>
                                    <h6 class="mb-1">张同学</h6>
                                    <small class="text-muted">申请时间：2025-01-21 10:30</small>
                                </div>
                                <div class="ms-auto">
                                    <span class="leave-status status-pending">待审批</span>
                                </div>
                            </div>
                            
                            <div class="course-info">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>课程：</strong>数学 - 代数基础
                                    </div>
                                    <div class="col-md-6">
                                        <strong>时间：</strong>2025-01-22 09:00-10:30
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-6">
                                        <strong>教室：</strong>A101
                                    </div>
                                    <div class="col-md-6">
                                        <strong>联系方式：</strong>138****5678
                                    </div>
                                </div>
                            </div>
                            
                            <div class="reason-text">
                                <strong>请假原因：</strong>身体不适，需要看医生，预计需要休息一天。
                            </div>
                            
                            <div class="approval-actions">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <textarea class="form-control" placeholder="审批意见（可选）..." rows="2"></textarea>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <button class="btn btn-approve me-2" onclick="approveLeave(this, '张同学', '数学')">
                                            <i class="fa fa-check"></i> 批准
                                        </button>
                                        <button class="btn btn-reject" onclick="rejectLeave(this, '张同学', '数学')">
                                            <i class="fa fa-times"></i> 拒绝
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 已批准申请 -->
                        <div class="leave-request" data-status="approved">
                            <div class="student-info">
                                <div class="student-avatar">王</div>
                                <div>
                                    <h6 class="mb-1">王同学</h6>
                                    <small class="text-muted">申请时间：2025-01-20 15:20</small>
                                </div>
                                <div class="ms-auto">
                                    <span class="leave-status status-approved">已批准</span>
                                </div>
                            </div>
                            
                            <div class="course-info">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>课程：</strong>数学 - 几何基础
                                    </div>
                                    <div class="col-md-6">
                                        <strong>时间：</strong>2025-01-21 14:00-15:30
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-6">
                                        <strong>教室：</strong>A102
                                    </div>
                                    <div class="col-md-6">
                                        <strong>审批时间：</strong>2025-01-20 16:00
                                    </div>
                                </div>
                            </div>
                            
                            <div class="reason-text">
                                <strong>请假原因：</strong>家庭事务，需要陪同家长办事。
                            </div>
                            
                            <div class="approval-actions">
                                <div class="alert alert-success mb-0">
                                    <i class="fa fa-check-circle"></i> 
                                    <strong>审批意见：</strong>同意请假，注意补课安排。
                                </div>
                            </div>
                        </div>

                        <!-- 已拒绝申请 -->
                        <div class="leave-request" data-status="rejected">
                            <div class="student-info">
                                <div class="student-avatar">李</div>
                                <div>
                                    <h6 class="mb-1">李同学</h6>
                                    <small class="text-muted">申请时间：2025-01-19 11:15</small>
                                </div>
                                <div class="ms-auto">
                                    <span class="leave-status status-rejected">已拒绝</span>
                                </div>
                            </div>
                            
                            <div class="course-info">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>课程：</strong>数学 - 综合练习
                                    </div>
                                    <div class="col-md-6">
                                        <strong>时间：</strong>2025-01-19 16:00-17:30
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-6">
                                        <strong>教室：</strong>A101
                                    </div>
                                    <div class="col-md-6">
                                        <strong>审批时间：</strong>2025-01-19 11:30
                                    </div>
                                </div>
                            </div>
                            
                            <div class="reason-text">
                                <strong>请假原因：</strong>其他安排，临时有事。
                            </div>
                            
                            <div class="approval-actions">
                                <div class="alert alert-danger mb-0">
                                    <i class="fa fa-times-circle"></i> 
                                    <strong>拒绝原因：</strong>请假时间过晚，且理由不够充分。
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../web/js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../web/bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 筛选请假申请
        function filterRequests(status) {
            // 更新筛选按钮状态
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 筛选申请列表
            const requests = document.querySelectorAll('.leave-request');
            requests.forEach(request => {
                if (status === 'all' || request.dataset.status === status) {
                    request.style.display = 'block';
                } else {
                    request.style.display = 'none';
                }
            });
        }

        // 批准请假
        function approveLeave(button, student, subject) {
            const request = button.closest('.leave-request');
            const comment = request.querySelector('textarea').value;
            
            if (confirm(`确认批准 ${student} 的 ${subject} 课请假申请吗？`)) {
                // 更新状态
                request.dataset.status = 'approved';
                request.querySelector('.leave-status').className = 'leave-status status-approved';
                request.querySelector('.leave-status').textContent = '已批准';
                
                // 更新审批区域
                const approvalActions = request.querySelector('.approval-actions');
                approvalActions.innerHTML = `
                    <div class="alert alert-success mb-0">
                        <i class="fa fa-check-circle"></i> 
                        <strong>审批意见：</strong>${comment || '同意请假'}
                    </div>
                `;
                
                alert('请假申请已批准');
                // 这里应该调用API更新请假状态
            }
        }

        // 拒绝请假
        function rejectLeave(button, student, subject) {
            const request = button.closest('.leave-request');
            const reason = prompt('请输入拒绝原因：');
            
            if (reason && reason.trim()) {
                // 更新状态
                request.dataset.status = 'rejected';
                request.querySelector('.leave-status').className = 'leave-status status-rejected';
                request.querySelector('.leave-status').textContent = '已拒绝';
                
                // 更新审批区域
                const approvalActions = request.querySelector('.approval-actions');
                approvalActions.innerHTML = `
                    <div class="alert alert-danger mb-0">
                        <i class="fa fa-times-circle"></i> 
                        <strong>拒绝原因：</strong>${reason}
                    </div>
                `;
                
                alert('请假申请已拒绝');
                // 这里应该调用API更新请假状态
            }
        }

        // 搜索功能
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const requests = document.querySelectorAll('.leave-request');
            
            requests.forEach(request => {
                const studentName = request.querySelector('h6').textContent.toLowerCase();
                const courseName = request.querySelector('.course-info').textContent.toLowerCase();
                
                if (studentName.includes(searchTerm) || courseName.includes(searchTerm)) {
                    request.style.display = 'block';
                } else {
                    request.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
