<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生主页 - 特靠谱教培系统</title>
    <!-- Bootstrap CSS -->
    <link href="../../web/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../../web/css/font-awesome.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .feature-card {
            text-align: center;
            padding: 30px 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .feature-card:hover {
            background-color: #f8f9fa;
        }
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #667eea;
        }
        .schedule-item {
            border-left: 4px solid #667eea;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
            border-radius: 0 8px 8px 0;
        }
        .schedule-time {
            font-weight: bold;
            color: #667eea;
        }
        .schedule-subject {
            font-size: 1.1rem;
            margin: 5px 0;
        }
        .schedule-teacher {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .quick-stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fa fa-graduation-cap"></i> 特靠谱教培系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.html">
                            <i class="fa fa-home"></i> 主页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="schedule.html">
                            <i class="fa fa-calendar"></i> 课表
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="leave.html">
                            <i class="fa fa-file-text"></i> 请假
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-user"></i> 张同学
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fa fa-user"></i> 个人信息</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fa fa-cog"></i> 设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../index.html"><i class="fa fa-sign-out"></i> 退出</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 欢迎信息 -->
        <div class="row">
            <div class="col-12">
                <div class="quick-stats">
                    <h4><i class="fa fa-user"></i> 欢迎回来，张同学！</h4>
                    <p class="mb-0">今天是 <span id="currentDate"></span>，祝你学习愉快！</p>
                </div>
            </div>
        </div>

        <!-- 快速统计 -->
        <div class="row mb-4">
            <div class="col-md-3 col-6 mb-3">
                <div class="card">
                    <div class="card-body stat-item">
                        <div class="stat-number text-primary">12</div>
                        <div class="stat-label">本周课程</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="card">
                    <div class="card-body stat-item">
                        <div class="stat-number text-success">8</div>
                        <div class="stat-label">已完成</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="card">
                    <div class="card-body stat-item">
                        <div class="stat-number text-warning">2</div>
                        <div class="stat-label">请假申请</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-6 mb-3">
                <div class="card">
                    <div class="card-body stat-item">
                        <div class="stat-number text-info">95%</div>
                        <div class="stat-label">出勤率</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 今日课程 -->
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-calendar-day"></i> 今日课程</h5>
                    </div>
                    <div class="card-body">
                        <div class="schedule-item">
                            <div class="schedule-time">09:00 - 10:30</div>
                            <div class="schedule-subject">数学 - 代数基础</div>
                            <div class="schedule-teacher"><i class="fa fa-user"></i> 李老师 | <i class="fa fa-map-marker"></i> 教室A101</div>
                        </div>
                        <div class="schedule-item">
                            <div class="schedule-time">14:00 - 15:30</div>
                            <div class="schedule-subject">英语 - 语法练习</div>
                            <div class="schedule-teacher"><i class="fa fa-user"></i> 王老师 | <i class="fa fa-map-marker"></i> 教室B203</div>
                        </div>
                        <div class="schedule-item">
                            <div class="schedule-time">16:00 - 17:30</div>
                            <div class="schedule-subject">物理 - 力学实验</div>
                            <div class="schedule-teacher"><i class="fa fa-user"></i> 张老师 | <i class="fa fa-map-marker"></i> 实验室C301</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速功能 -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-bolt"></i> 快速功能</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="feature-card" onclick="location.href='schedule.html'">
                            <div class="feature-icon">
                                <i class="fa fa-calendar"></i>
                            </div>
                            <h6>查看课表</h6>
                            <p class="text-muted mb-0">查看完整课程安排</p>
                        </div>
                        <hr class="my-0">
                        <div class="feature-card" onclick="location.href='leave.html'">
                            <div class="feature-icon">
                                <i class="fa fa-file-text"></i>
                            </div>
                            <h6>申请请假</h6>
                            <p class="text-muted mb-0">提交请假申请</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近请假记录 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fa fa-history"></i> 最近请假记录</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>申请时间</th>
                                        <th>请假课程</th>
                                        <th>请假原因</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2025-01-20</td>
                                        <td>数学 - 代数基础</td>
                                        <td>身体不适</td>
                                        <td><span class="badge bg-success">已批准</span></td>
                                    </tr>
                                    <tr>
                                        <td>2025-01-18</td>
                                        <td>英语 - 语法练习</td>
                                        <td>家庭事务</td>
                                        <td><span class="badge bg-warning">待审批</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="../../web/js/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="../../web/bootstrap/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 显示当前日期
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });
    </script>
</body>
</html>
